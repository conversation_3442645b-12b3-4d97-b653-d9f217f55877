import React from 'react';
import assets from "../../../public/assets";

export default function HeroSection() {
    return (

        <div className="relative z-10 container mx-auto px-4 py-10">
            <div className="flex flex-col items-center justify-center min-h-[80vh] space-y-16">
                {/* Hero content - centered */}
                <div className="text-center space-y-8 max-w-4xl">
                    <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                        Discover & Collect{' '}
                        <span className="bg-gradient-to-r from-primary-pink to-primary-purple bg-clip-text text-transparent">
                            Extraordinary NFTs
                        </span>
                    </h1>

                    <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                        Enter the world of digital art and collectibles. Explore unique NFTs created by artists worldwide.
                    </p>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <button className="bg-gradient-to-r from-primary-pink to-primary-purple hover:from-purple-600 hover:to-pink-600 text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                             Start Creating
                        </button>

                        <button className="border-1 border-[#374151] hover:border-white text-gray-300 hover:text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 flex items-center justify-center gap-2">
                            ▶ Watch Demo
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}
