import React from 'react';
import MintForm from './MintForm';

export default function HeroSection() {
    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-purple-900/20 via-slate-900/50 to-slate-900"></div>

            <div className="relative z-10 container mx-auto px-4 py-20">
                <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
                    {/* Left side - Hero content */}
                    <div className="text-center lg:text-left space-y-8">
                        <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                            Discover & Collect{' '}
                            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                                Extraordinary NFTs
                            </span>
                        </h1>

                        <p className="text-xl text-gray-300 max-w-2xl">
                            Enter the world of digital art and collectibles. Explore unique NFTs created by artists worldwide.
                        </p>

                        <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                            <button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                                ✨ Start Creating
                            </button>

                            <button className="border-2 border-gray-400 hover:border-white text-gray-300 hover:text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 flex items-center justify-center gap-2">
                                ▶ Watch Demo
                            </button>
                        </div>
                    </div>

                    {/* Right side - Mint Form */}
                    <div className="flex justify-center lg:justify-end">
                        <MintForm />
                    </div>
                </div>
            </div>
        </div>
    );
}
