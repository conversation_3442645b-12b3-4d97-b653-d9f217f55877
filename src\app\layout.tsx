import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { getDefaultConfig, RainbowKitProvider } from '@rainbow-me/rainbowkit';
import '@rainbow-me/rainbowkit/styles.css';
import { WagmiProvider } from 'wagmi';
import { sepolia } from 'wagmi/chains';
import DappProvider from "@/providers/DappProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {


  return (
    <DappProvider>
      <html lang="en">
        <body className={`${geistSans.variable} antialiased`}>
          {children}
        </body>
      </html>
    </DappProvider>

  );
}
