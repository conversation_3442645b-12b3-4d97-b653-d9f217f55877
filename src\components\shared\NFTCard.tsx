import React from 'react';
import Image, { StaticImageData } from 'next/image';

interface NFTCardProps {
    id: string;
    title: string;
    description: string;
    imageUrl: StaticImageData;
    creator?: string;
    price?: number;
    className?: string;
}

export default function NFTCard({
    id,
    title,
    description,
    imageUrl,
    creator,
    price,
    className = ""
}: NFTCardProps) {
    return (
        <div className={`group relative bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/10 ${className}`}>
            {/* NFT Image */}
            <div className="relative aspect-[4/3] overflow-hidden">
                <Image
                    src={imageUrl}
                    alt={title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />

                {/* Gradient overlay on hover */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>

            {/* NFT Details */}
            <div className="p-4 space-y-2">
                <h3 className="text-lg font-bold text-white transition-colors duration-200 truncate">
                    {title}
                </h3>

                <p className="text-[#9CA3AF] text-sm line-clamp-2 leading-relaxed">
                    {description}
                </p>

            </div>

            {/* Hover effect border */}
            <div className="absolute inset-0 rounded-xl border-2 border-transparent group-hover:border-gradient-to-r group-hover:from-purple-500 group-hover:to-pink-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
        </div>
    );
}
