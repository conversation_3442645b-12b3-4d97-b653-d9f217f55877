'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

interface MintFormData {
    name: string;
    description: string;
    imageUrl: string;
}

// Validation schema using Yup
const validationSchema = yup.object({
    name: yup
        .string()
        .required('NFT name is required')
        .min(3, 'NFT name must be at least 3 characters')
        .max(50, 'NFT name must not exceed 50 characters'),
    description: yup
        .string()
        .required('Description is required')
        .min(10, 'Description must be at least 10 characters')
        .max(500, 'Description must not exceed 500 characters'),
    imageUrl: yup
        .string()
        .required('Image URL is required')
        .url('Please enter a valid URL')
        .matches(
            /\.(jpg|jpeg|png|gif|webp|svg)$/i,
            'Image URL must end with a valid image extension (jpg, jpeg, png, gif, webp, svg)'
        )
});

export default function MintForm() {
    const [isLoading, setIsLoading] = useState(false);

    // Initialize React Hook Form with Yup validation
    const {
        register,
        handleSubmit,
        formState: { errors, isValid },
        reset
    } = useForm<MintFormData>({
        resolver: yupResolver(validationSchema),
        mode: 'onChange', // Validate on change for better UX
        defaultValues: {
            name: '',
            description: '',
            imageUrl: ''
        }
    });

    const onSubmit = async (data: MintFormData) => {
        setIsLoading(true);

        try {
            // TODO: Implement actual minting logic here
            console.log('Minting NFT with data:', data);

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Reset form after successful mint
            reset();

            alert('NFT minted successfully!');
        } catch (error) {
            console.error('Error minting NFT:', error);
            alert('Error minting NFT. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="w-full max-w-2xl mx-auto">
            <div className="bg-slate-800/80 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 shadow-2xl">
                <h2 className="text-2xl font-bold text-white mb-6 text-center">
                    Mint Your NFT
                </h2>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                    {/* NFT Name */}
                    <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                            NFT Name
                        </label>
                        <input
                            type="text"
                            id="name"
                            {...register('name')}
                            placeholder="Enter NFT name"
                            className={`w-full px-4 py-3 bg-slate-700/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 ${errors.name
                                ? 'border-red-500 focus:ring-red-500'
                                : 'border-slate-600 focus:ring-purple-500'
                                }`}
                        />
                        {errors.name && (
                            <p className="mt-1 text-sm text-red-400">{errors.name.message}</p>
                        )}
                    </div>

                    {/* Description */}
                    <div>
                        <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-2">
                            Description
                        </label>
                        <textarea
                            id="description"
                            {...register('description')}
                            placeholder="Describe your NFT"
                            rows={4}
                            className={`w-full px-4 py-3 bg-slate-700/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 resize-none ${errors.description
                                ? 'border-red-500 focus:ring-red-500'
                                : 'border-slate-600 focus:ring-purple-500'
                                }`}
                        />
                        {errors.description && (
                            <p className="mt-1 text-sm text-red-400">{errors.description.message}</p>
                        )}
                    </div>

                    {/* Image URL */}
                    <div>
                        <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-300 mb-2">
                            Image URL
                        </label>
                        <input
                            type="url"
                            id="imageUrl"
                            {...register('imageUrl')}
                            placeholder="Enter image URL"
                            className={`w-full px-4 py-3 bg-slate-700/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 ${errors.imageUrl
                                ? 'border-red-500 focus:ring-red-500'
                                : 'border-slate-600 focus:ring-purple-500'
                                }`}
                        />
                        {errors.imageUrl && (
                            <p className="mt-1 text-sm text-red-400">{errors.imageUrl.message}</p>
                        )}
                    </div>

                    {/* Mint Button */}
                    <button
                        type="submit"
                        disabled={isLoading || !isValid}
                        className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 disabled:from-gray-500 disabled:to-gray-600 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 disabled:scale-100 shadow-lg disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    >
                        {isLoading ? (
                            <>
                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                                Minting...
                            </>
                        ) : (
                            <>
                                🎨 Mint NFT
                            </>
                        )}
                    </button>
                </form>
            </div>
        </div>
    );
}
